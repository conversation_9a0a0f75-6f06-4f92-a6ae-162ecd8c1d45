#!/usr/bin/env python3
"""
专门测试基础 panzoom 特效的抖动修复
"""

import os
import sys
import tempfile
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, '/Users/<USER>/ai-video')

from image2clip import _make_clip_for_single_image
from modules.video_effects import select_effect

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def force_basic_panzoom_test():
    """强制测试基础 panzoom 特效"""
    
    # 测试图片路径
    high_res_image = "/Users/<USER>/ai-video/1-theme-talker/results/Putten_murder_case_Netherlands/images/Buorren_Putten_by_de_tsjerke_1.JPG"
    
    # 检查文件是否存在
    if not os.path.exists(high_res_image):
        logger.error(f"测试图片不存在: {high_res_image}")
        return False
    
    # 创建输出目录
    output_dir = Path("/Users/<USER>/ai-video/test_outputs")
    output_dir.mkdir(exist_ok=True)
    
    try:
        # 获取图片尺寸
        import subprocess
        result = subprocess.run([
            "ffprobe", "-v", "error", 
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height",
            "-of", "csv=p=0", high_res_image
        ], capture_output=True, text=True, check=True)
        
        width_str, height_str = result.stdout.strip().split(',')
        image_width, image_height = int(width_str), int(height_str)
        
        logger.info(f"图片尺寸: {image_width}x{image_height}")
        
        # 模拟一个会选择基础特效的场景
        # 我们需要修改 select_effect 的逻辑或者直接构造基础特效配置
        
        # 创建一个模拟的基础 panzoom 特效配置
        basic_effect_config = {
            "is_advanced": False,
            "pan_x": 10,  # 水平平移速度
            "pan_y": 0,   # 垂直平移速度
        }
        
        # 测试短时长，这样更可能触发基础特效
        duration = 2.0  # 2秒
        
        logger.info("=== 测试基础 Panzoom 特效 (模拟) ===")
        
        # 直接调用内部函数，传入模拟的基础特效
        output_path = output_dir / "test_basic_panzoom_fixed.mp4"
        
        # 我们需要手动模拟基础特效的处理逻辑
        # 由于当前的 select_effect 总是返回高级特效，我们需要直接测试基础逻辑
        
        # 创建一个临时的测试函数
        def test_basic_panzoom_directly():
            from image2clip import DEFAULT_ZOOM_START, DEFAULT_ZOOM_END
            from modules.video_effects import CONFIG as EFFECTS_CONFIG
            from config import VIDEO_RESOLUTION, VIDEO_FPS
            import tempfile
            
            temp_dir = tempfile.mkdtemp()
            
            try:
                # 模拟基础 panzoom 的处理逻辑
                width, height = VIDEO_RESOLUTION
                fps = VIDEO_FPS
                
                # 获取基础 panzoom 的 zoom 配置
                basic_cfg = EFFECTS_CONFIG.get('basic_panzoom', {})
                zoom_start = basic_cfg.get('zoom_start', DEFAULT_ZOOM_START)
                zoom_end = basic_cfg.get('zoom_end', DEFAULT_ZOOM_END)
                
                pan_speed_x = 10  # 模拟水平平移
                pan_speed_y = 0
                
                logger.info(f"使用基础 panzoom 参数: zoom {zoom_start}->{zoom_end}, pan_x={pan_speed_x}")
                
                # 预处理图片
                display_aspect_ratio = f"{width}:{height}"
                temp_image = os.path.join(temp_dir, 'processed_image.jpg')
                
                preprocess_filter = (
                    "split[original][for_bg];"
                    f"[for_bg]scale={width*2}:{height*2}:force_original_aspect_ratio=increase,"
                    f"crop={width}:{height},"
                    f"gblur=sigma=20[bg];"
                    f"[original]scale={width}:{height}:force_original_aspect_ratio=decrease,"
                    f"pad={width}:{height}:(ow-iw)/2:(oh-ih)/2:color=black@0[fg];"
                    "[bg][fg]overlay=x=(W-w)/2:y=(H-h)/2"
                )
                
                preprocess_command = [
                    'ffmpeg', '-y',
                    '-i', high_res_image,
                    '-vf', preprocess_filter,
                    '-q:v', '2',
                    '-frames:v', '1',
                    '-update', '1',
                    '-pix_fmt', 'yuv420p',  # 强制使用标准像素格式
                    temp_image
                ]
                subprocess.run(preprocess_command, check=True, capture_output=True, text=True)
                
                # 生成视频 (应用我们的修复)
                total_frames = int(duration * fps)
                
                # 对高分辨率图片使用更温和的缩放范围
                if image_width * image_height > 2000000:  # 高分辨率图片
                    zoom_end_adjusted = zoom_start + (zoom_end - zoom_start) * 0.5
                    logger.debug(f"高分辨率图片，调整缩放范围: {zoom_start} -> {zoom_end_adjusted}")
                else:
                    zoom_end_adjusted = zoom_end
                
                # 使用余弦函数创建更平滑的缩放曲线
                zoom_expr = f"{zoom_start}+({zoom_end_adjusted}-{zoom_start})*(1-cos(PI*on/{total_frames}))/2" if total_frames > 0 else f"{zoom_start}"
                
                # 对高分辨率图片降低平移速度
                if image_width * image_height > 2000000:
                    pan_speed_x = pan_speed_x * 0.5
                    pan_speed_y = pan_speed_y * 0.5
                    logger.debug(f"高分辨率图片，降低平移速度: pan_x={pan_speed_x}, pan_y={pan_speed_y}")
                
                # 使用余弦函数创建更平滑的运动曲线
                if pan_speed_x != 0:
                    x_pan_term = f"+{pan_speed_x}*(1-cos(PI*on/{total_frames}))/2"
                else:
                    x_pan_term = ""
                
                x_expr = f"(iw-iw/zoom)/2{x_pan_term}" if pan_speed_x != 0 else f"(iw-iw/zoom)/2"
                y_expr = f"(ih-ih/zoom)/2"
                
                # 动态调整放大倍数
                if image_width * image_height > 2000000:
                    scale_factor_for_panzoom = 2
                    logger.debug(f"高分辨率图片，使用较低的放大倍数: {scale_factor_for_panzoom}")
                else:
                    scale_factor_for_panzoom = 4
                
                scale_width = width * scale_factor_for_panzoom
                scale_height = height * scale_factor_for_panzoom
                
                video_filter = (
                    f"scale={scale_width}:{scale_height}:force_original_aspect_ratio=increase,"
                    f"crop={scale_width}:{scale_height}:(in_w-{scale_width})/2:(in_h-{scale_height})/2,"
                    f"zoompan="
                    f"z='{zoom_expr}':"
                    f"x='{x_expr}':"
                    f"y='{y_expr}':"
                    f"d=1:"
                    f"s={width}x{height},"
                    f"setsar=1:1,"
                    f"setdar={width}/{height},"
                    f"trim=duration={duration},"
                    f"fps={fps},"
                    f"format=yuv420p"
                )
                
                # 构建FFmpeg命令
                from config import VIDEO_CODEC, VIDEO_PRESET, VIDEO_CRF, VIDEO_BITRATE
                
                # 对高分辨率图片使用更好的编码参数
                if image_width * image_height > 2000000:
                    crf_adjusted = max(VIDEO_CRF - 2, 18)
                    preset_adjusted = 'slow'
                    logger.debug(f"高分辨率图片，使用高质量编码: CRF={crf_adjusted}, preset={preset_adjusted}")
                else:
                    crf_adjusted = VIDEO_CRF
                    preset_adjusted = VIDEO_PRESET
                
                ffmpeg_command = [
                    'ffmpeg', '-y',
                    '-hide_banner',
                    '-loglevel', 'error',
                    '-stats',
                    '-stats_period', '10',
                    '-thread_queue_size', '512',
                    '-loop', '1',
                    '-i', temp_image,
                    '-filter_complex', video_filter,
                    '-c:v', VIDEO_CODEC,
                    '-preset', preset_adjusted,
                    '-crf', str(crf_adjusted),
                    '-r', str(fps),
                    '-b:v', VIDEO_BITRATE,
                    '-pix_fmt', 'yuv420p',
                    '-aspect', display_aspect_ratio,
                    '-movflags', '+faststart',
                    '-g', str(fps * 2),
                    '-threads', '0',
                    '-vsync', 'cfr',
                    str(output_path)
                ]
                
                logger.debug("执行修复后的基础 Panzoom FFmpeg 命令")
                start_time = time.time()
                subprocess.run(ffmpeg_command, check=True)
                logger.info(f"基础 Panzoom 测试完成，耗时: {time.time() - start_time:.2f}秒")
                
                return True
                
            finally:
                # 清理临时目录
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
        
        # 运行测试
        import time
        success = test_basic_panzoom_directly()
        
        if success and os.path.exists(output_path):
            file_size = os.path.getsize(output_path) / (1024 * 1024)
            logger.info(f"基础 Panzoom 测试成功!")
            logger.info(f"输出文件: {output_path} ({file_size:.2f}MB)")
            logger.info(f"请检查视频是否还有抖动问题")
            return True
        else:
            logger.error("基础 Panzoom 测试失败")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        import traceback
        logger.error("错误堆栈:\n" + traceback.format_exc())
        return False

if __name__ == "__main__":
    logger.info("开始基础 Panzoom 抖动修复测试")
    
    success = force_basic_panzoom_test()
    
    if success:
        logger.info("基础 Panzoom 测试完成！")
    else:
        logger.error("基础 Panzoom 测试失败")
        sys.exit(1)
