# modules/video_effects.py
import numpy as np
import cv2
import os
import tempfile
import logging
import random
import subprocess
import shutil
from typing import Tuple, Dict, Callable, Any, List, Union, Optional

# 新增：导入常量
from config import VIDEO_RESOLUTION, VIDEO_FPS, logger
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO) # Explicitly set level for this logger

# 新增：基础 panzoom 效果参数
DEFAULT_ZOOM_START = 1.0
DEFAULT_ZOOM_END = 1.2
DEFAULT_PAN_SPEED_FACTOR = 10  # 降低以减少抖动

def strip_metadata_with_exiftool(image_path: str):
    """
    无损剥离图片所有 EXIF / 元数据。依赖：ExifTool。

    注意: 处理非标准格式图片时会优雅地处理错误。
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            logger.warning(f"文件不存在，无法剥离元数据: {image_path}")
            return image_path

        # 尝试执行exiftool命令
        result = subprocess.run(
            ["exiftool", "-all=", "-overwrite_original", image_path],
            check=False,  # 改为False，不自动抛出异常
            capture_output=True,
            text=True
        )

        # 检查执行结果
        if result.returncode != 0:
            logger.warning(f"剥离元数据时出错，可能是不支持的格式: {image_path}")
            logger.debug(f"ExifTool错误: {result.stderr}")
            # 继续处理，不中断流程

        return image_path
    except Exception as e:
        logger.warning(f"剥离元数据时发生异常: {str(e)}，继续处理")
        return image_path

logger = logging.getLogger(__name__)

# ——————————————————————————————————————————————————————————————
# 1. 全局配置 和 特效目录
# ——————————————————————————————————————————————————————————————
CONFIG = {
    'FPS': VIDEO_FPS, # 使用 config.py 中的值
    'LOW_RES_FACTOR': 2,    # 遮罩生成时先降采样再放大
    'effect_A': {
        'duration': 0.6,
        'ratio': 0.5,
        'speed_factor': 0.7,  # 原来 1.0 -> 0.7 更平滑
    },
    'effect_B': {
        'duration': 0.5,
        'ratio': 0.33,
        'zoom_max': 1.1,
    },
    'effect_C': {
        'duration': 0.7,
        'ratio': 0.382,
        'distance_factor': 0.125,  # 原来 0.25 -> 0.125 更轻微
    },
    'effect_D': {
        'duration': 0.4,
        'ratio': 1.0,         # not used anymore, placeholder
        'speed_factor': None, # no longer used
        'zoom': 1.5,
    },
    'effect_F': {
        'duration': 1.0, # 这是配置，不是最低要求
        'ratio': 0.5,
        'zoom_max': 1.2,
        'phase1': 0.3,
        'phase2': 0.7,
    },
    # 新增：基础 panzoom 效果的配置（如果需要特定参数）
    'basic_panzoom': {
        'zoom_start': DEFAULT_ZOOM_START,
        'zoom_end': DEFAULT_ZOOM_END,
        'pan_speed_factor': DEFAULT_PAN_SPEED_FACTOR,
    },
    # vertical 特效配置 - 简化为无需特殊配置
    'vertical': {
    }
}

# 特效目录: 管理所有可用特效及其属性
EFFECTS_CATALOG: Dict[str, Dict[str, Any]] = {
    # --- 高级特效 ---
    "A": {"func": "effect_A_ffmpeg", "min_duration": 0.6, "is_advanced": True},
    "B": {"func": "effect_B_ffmpeg", "min_duration": 0.5, "is_advanced": True},
    "C": {"func": "effect_C_ffmpeg", "min_duration": 0.7, "is_advanced": True},
    "D": {"func": "effect_D_ffmpeg", "min_duration": 0.4, "is_advanced": True},
    "F": {"func": "effect_F_ffmpeg", "min_duration": 5.0, "is_advanced": True},
    "vertical": {"func": "effect_vertical_ffmpeg", "min_duration": 0.5, "is_advanced": True},
    # --- 基础 Panzoom 特效 ---
    # 注意：基础 panzoom 的 func 设为 None，表示由调用方 (image2clip.py) 直接处理
    # "pan_right": {"func": None, "min_duration": 0.1, "is_advanced": False, "pan_x": DEFAULT_PAN_SPEED_FACTOR, "pan_y": 0},
    # "pan_down":  {"func": None, "min_duration": 0.1, "is_advanced": False, "pan_x": 0, "pan_y": DEFAULT_PAN_SPEED_FACTOR},
    # "pan_left":  {"func": None, "min_duration": 0.1, "is_advanced": False, "pan_x": -DEFAULT_PAN_SPEED_FACTOR, "pan_y": 0},
    # "pan_up":    {"func": None, "min_duration": 0.1, "is_advanced": False, "pan_x": 0, "pan_y": -DEFAULT_PAN_SPEED_FACTOR},
}

# 缓存已生成的 mask
_MASK_CACHE = {}

# 缓存上一次用于方形图片的vertical方向（用于轮换上下方向）
_LAST_VERTICAL_DIRECTION = "down"  # 默认初始方向为向下

# FFmpeg安全速度范围，超出这个范围会导致解析错误
MAX_FFMPEG_SPEED = 100      # 最大安全速度值
MIN_FFMPEG_SPEED = -100     # 最小安全速度值
MIN_PHASE_DURATION = 0.5    # 最小阶段持续时间(秒)

# Threshold for preferring effect D on ultra-wide images
D_PREFER_DURATION_THRESHOLD = 5.0  # seconds

def limit_speed(speed: Union[int, float]) -> int:
    """
    限制滚动速度在FFmpeg安全范围内

    Args:
        speed: 原始速度值

    Returns:
        int: 限制后的安全速度值
    """
    # 确保为整数
    speed_int = int(speed)

    # 限制在安全范围内
    if speed_int > MAX_FFMPEG_SPEED:
        logger.warning(f"速度值 {speed_int} 过大，限制为 {MAX_FFMPEG_SPEED}")
        return MAX_FFMPEG_SPEED
    elif speed_int < MIN_FFMPEG_SPEED:
        logger.warning(f"速度值 {speed_int} 过小，限制为 {MIN_FFMPEG_SPEED}")
        return MIN_FFMPEG_SPEED

    return speed_int

# ——————————————————————————————————————————————————————————————
# 2. 遮罩生成 & 缓存
# ——————————————————————————————————————————————————————————————
def get_mask(size, ratio, shape="circular", origin=(0.5, 0.5),
             direction="horizontal", low_res_factor=1):
    """
    参数检查 + 缓存 + 调用 make_mask_raw 生成 mask。
    """
    key = (size, ratio, shape, origin, direction, low_res_factor)
    if key in _MASK_CACHE:
        return _MASK_CACHE[key]
    # 生成
    mask = make_mask_raw(size, ratio, shape, origin, direction, low_res_factor)
    _MASK_CACHE[key] = mask
    return mask

def make_mask_raw(size, ratio, shape, origin, direction, low_res_factor):
    """
    生成单通道 float32 遮罩 [0,1]。
    - shape: "circular" or "linear"
    - direction: "horizontal", "vertical", or "diagonal" (only for linear)
    - low_res_factor: >1 时先在低分辨率生成再放大
    """
    # 参数校验
    if shape not in ("circular", "linear"):
        raise ValueError(f"Unsupported shape: {shape}")
    if direction not in ("horizontal", "vertical", "diagonal"):
        raise ValueError(f"Unsupported direction: {direction}")

    w, h = size
    # 低分辨率生成
    if low_res_factor > 1:
        w0, h0 = w // low_res_factor, h // low_res_factor
    else:
        w0, h0 = w, h

    cx, cy = int(w0 * origin[0]), int(h0 * origin[1])
    ys, xs = np.ogrid[:h0, :w0]

    if shape == "circular":
        dist = np.sqrt((xs - cx)**2 + (ys - cy)**2)
        maxd = ratio * min(w0, h0)
        m = 1 - np.clip(dist / maxd, 0, 1)
    else:  # linear or diagonal
        if direction == "horizontal":
            m = 1 - np.clip(np.abs(xs - cx) / (w0 * ratio), 0, 1)
        elif direction == "vertical":
            m = 1 - np.clip(np.abs(ys - cy) / (h0 * ratio), 0, 1)
        else:  # diagonal
            dist = np.abs(xs - cx) + np.abs(ys - cy)
            m = 1 - np.clip(dist / (max(w0, h0) * ratio), 0, 1)

    mask = m.astype('float32')

    # 放大回原分辨率
    if low_res_factor > 1:
        mask = cv2.resize(mask, (w, h), interpolation=cv2.INTER_LINEAR)
    return mask

# ——————————————————————————————————————————————————————————————
# 3. 使用FFmpeg实现各种动画效果
# ——————————————————————————————————————————————————————————————

def apply_effect_for_image(
    effect_key: str, # 改为接收特效 key
    image_path: str,
    output_path: str,
    width: int,
    height: int,
    duration: float,
    fps: int,
    # prev_effect: str = None, # 不再需要 prev_effect
    temp_dir: str, # 新增 temp_dir 参数
    effect_params: Dict[str, Any] = None, # 新增：特效参数字典
) -> Tuple[str, str]:
    """
    为单张图片应用指定的高级特效，生成短视频片段
    (不再处理基础 panzoom，也不再进行特效选择和检查)

    Args:
        effect_key: 要应用的特效的键 (来自 EFFECTS_CATALOG)
        image_path: 图片路径
        output_path: 输出视频路径
        width: 视频宽度
        height: 视频高度
        duration: 视频时长
        fps: 帧率
        temp_dir: 临时目录路径
        effect_params: 特效的额外参数字典，例如 vertical 的 direction

    Returns:
        Tuple[str, str]: (输出视频路径, 使用的特效键)

    Raises:
        ValueError: 如果 effect_key 无效或不是高级特效。
        Exception: FFmpeg 执行或其他错误。
    """
    if effect_key not in EFFECTS_CATALOG or not EFFECTS_CATALOG[effect_key].get("is_advanced"):
        raise ValueError(f"无效或非高级特效键: {effect_key}")

    # 获取特效函数名
    func_name = EFFECTS_CATALOG[effect_key]["func"]
    if not func_name: # 理论上不会发生，因为上面检查了 is_advanced
         raise ValueError(f"特效 '{effect_key}' 没有关联的处理函数。")

    effect_func = globals().get(func_name)
    if not effect_func:
        raise ValueError(f"找不到特效函数: {func_name}")

    # 在应用特效前，确保图片格式正确
    processed_image_path = ensure_valid_image_format(image_path)

    # 剥离图片元数据 (可选，但保留)
    strip_metadata_with_exiftool(processed_image_path)

    logger.info(f"应用高级效果 {effect_key} 到图片 {processed_image_path}")

    try:
        # 准备特效参数
        effect_params = effect_params or {}

        # 调用具体的 effect_X_ffmpeg 函数，传递额外参数
        output = effect_func(
            image_path=processed_image_path,
            output_path=output_path,
            width=width,
            height=height,
            duration=duration,
            fps=fps,
            temp_dir=temp_dir, # 传递 temp_dir
            **effect_params  # 展开额外参数
        )

        # 如果创建了临时转换文件，且与原始文件不同，则清理
        if processed_image_path != image_path and os.path.exists(processed_image_path):
            try:
                os.remove(processed_image_path)
                logger.debug(f"已删除临时转换图片文件: {processed_image_path}")
            except OSError as e:
                logger.warning(f"无法删除临时图片文件 {processed_image_path}: {e}")

        return output, effect_key
    except Exception as e:
        logger.error(f"应用效果 {effect_key} 时出错: {str(e)}")

        # 如果创建了临时转换文件，确保清理
        if processed_image_path != image_path and os.path.exists(processed_image_path):
            try:
                os.remove(processed_image_path)
            except:
                pass

        raise

def ensure_valid_image_format(image_path: str) -> str:
    """
    确保图片格式是可处理的，如果是WebP等格式会转换为JPG
    同时对高分辨率图片进行预处理以避免抖动问题

    Args:
        image_path: 原始图片路径

    Returns:
        str: 处理后的图片路径
    """
    if not os.path.exists(image_path):
        logger.error(f"图片文件不存在: {image_path}")
        raise FileNotFoundError(f"图片文件不存在: {image_path}")

    # 首先获取图片尺寸信息
    try:
        result = subprocess.run([
            "ffprobe", "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height,pix_fmt",
            "-of", "csv=p=0", image_path
        ], capture_output=True, text=True, check=True)

        # ffprobe输出格式: width,height,pix_fmt
        width_str, height_str, pix_fmt = result.stdout.strip().split(',')
        image_width, image_height = int(width_str), int(height_str)
        is_high_res = image_width * image_height > 2000000  # 2MP阈值

        logger.debug(f"图片信息: {image_width}x{image_height}, 像素格式: {pix_fmt}, 高分辨率: {is_high_res}")

    except Exception as e:
        logger.warning(f"无法获取图片信息: {e}，使用原文件")
        return image_path

    # 检查是否需要处理（格式转换或高分辨率优化）
    need_format_conversion = False
    need_high_res_optimization = is_high_res

    # 检查文件格式
    try:
        # 使用file命令检查实际文件类型
        result = subprocess.run(
            ["file", "--mime-type", image_path],
            check=True,
            capture_output=True,
            text=True
        )

        mime_type = result.stdout.split(":", 1)[1].strip()

        # 如果不是标准图片格式(jpg/png)，需要转换
        if not (mime_type.endswith('/jpeg') or mime_type.endswith('/png') or mime_type.endswith('/jpg')):
            need_format_conversion = True
            logger.debug(f"检测到非标准图片格式: {mime_type}")

        # 检查像素格式是否需要标准化
        if pix_fmt in ['yuvj422p', 'yuvj420p', 'yuvj444p']:
            need_format_conversion = True
            logger.debug(f"检测到需要标准化的像素格式: {pix_fmt}")

    except Exception as e:
        logger.warning(f"检查图片格式时出错: {str(e)}")

    # 如果不需要任何处理，直接返回原文件
    if not need_format_conversion and not need_high_res_optimization:
        return image_path

    # 创建处理后的文件路径
    dir_path = os.path.dirname(image_path)
    base_name = os.path.basename(image_path)
    name, _ = os.path.splitext(base_name)

    if need_high_res_optimization:
        processed_path = os.path.join(dir_path, f"{name}_optimized.jpg")
        logger.info(f"高分辨率图片预处理: {image_path} -> {processed_path}")
    else:
        processed_path = os.path.join(dir_path, f"{name}_conv.jpg")
        logger.info(f"图片格式转换: {image_path} -> {processed_path}")

    # 构建FFmpeg命令
    cmd = ["ffmpeg", "-y", "-i", image_path]

    # 对高分辨率图片应用优化处理
    if need_high_res_optimization:
        # 计算合适的缩放尺寸，保持宽高比
        # 将长边限制在合理范围内，避免过度处理
        max_dimension = 2048  # 最大边长限制
        if max(image_width, image_height) > max_dimension:
            if image_width > image_height:
                target_width = max_dimension
                target_height = int(image_height * max_dimension / image_width)
            else:
                target_height = max_dimension
                target_width = int(image_width * max_dimension / image_height)

            # 确保尺寸是偶数（FFmpeg要求）
            target_width = target_width - (target_width % 2)
            target_height = target_height - (target_height % 2)

            logger.debug(f"高分辨率图片缩放: {image_width}x{image_height} -> {target_width}x{target_height}")

            cmd.extend([
                "-vf", f"scale={target_width}:{target_height}:flags=lanczos",
                "-q:v", "2",  # 高质量JPEG
            ])
        else:
            # 不需要缩放，但仍需要格式标准化
            cmd.extend(["-q:v", "2"])
    else:
        # 仅格式转换，保持原始尺寸
        cmd.extend(["-q:v", "2"])

    # 强制使用标准像素格式
    cmd.extend([
        "-pix_fmt", "yuv420p",
        "-frames:v", "1",
        processed_path
    ])

    try:
        subprocess.run(cmd, check=True, capture_output=True)

        if os.path.exists(processed_path):
            # 验证处理后的文件
            new_size = os.path.getsize(processed_path)
            original_size = os.path.getsize(image_path)
            logger.debug(f"图片处理完成: {original_size/1024/1024:.2f}MB -> {new_size/1024/1024:.2f}MB")
            return processed_path
        else:
            logger.warning(f"图片处理失败，使用原文件: {image_path}")
            return image_path

    except subprocess.CalledProcessError as e:
        logger.error(f"图片处理失败: {e}")
        return image_path
    except Exception as e:
        logger.warning(f"图片处理时出错: {str(e)}，使用原文件")
        return image_path

def effect_A_ffmpeg(
    image_path: str,
    output_path: str,
    width: int,
    height: int,
    duration: float,
    fps: int,
    temp_dir: str
) -> str:
    """50% 中心径向遮罩 + 同步水平滚动 (完整平移)"""
    p = CONFIG['effect_A']
    ratio, sf = p['ratio'], p['speed_factor']

    # 创建遮罩图像
    mask_path = os.path.join(temp_dir, 'mask.png')
    create_mask_image((width, height), ratio, "circular", (0.5, 0.5),
                      "horizontal", CONFIG['LOW_RES_FACTOR'], mask_path)

    # 计算总帧数
    total_frames = int(fps * duration)

    # 计算显示宽高比
    display_aspect_ratio = f"{width}:{height}"

    # 平滑缩放+平移（overlay），从遮罩区域逐步放大至全图
    total_frames = int(fps * duration)  # 已计算
    filter_complex = (
        f"[0:v]scale={width}:{height}:force_original_aspect_ratio=increase,"
        f"crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[base];"
        f"[1:v]scale={width}:{height}[mask];"
        f"[base][mask]alphamerge[tmp];"
        # overlay: ease-out zoompan using cosine, pan同步
        f"[tmp]zoompan="
        f"z='1+{sf}*(1+cos(3.14159*on/{total_frames}))/2':"
        f"x='-({width}*{sf})*(1+cos(3.14159*on/{total_frames}))/2':"
        f"y=0:d={total_frames}:s={width}x{height},"
        f"setsar=1:1,setdar={width}/{height}[vid];"
        f"[vid]format=yuv420p[out]"
    )

    # 构建FFmpeg命令
    cmd = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-i", image_path,
        "-loop", "1", "-i", mask_path,
        "-filter_complex", filter_complex,
        "-map", "[out]",
        "-t", str(duration),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", display_aspect_ratio,
        output_path
    ]

    subprocess.run(cmd, check=True)
    return output_path

def effect_B_ffmpeg(
    image_path: str,
    output_path: str,
    width: int,
    height: int,
    duration: float,
    fps: int,
    temp_dir: str
) -> str:
    """33% 左上径向遮罩 + 动态放大 + ease-in-out"""
    p = CONFIG['effect_B']
    ratio, zmax = p['ratio'], p['zoom_max']

    # 创建遮罩图像
    mask_path = os.path.join(temp_dir, 'mask.png')
    create_mask_image((width, height), ratio, "circular", (0, 0),
                     "horizontal", CONFIG['LOW_RES_FACTOR'], mask_path)

    # 计算显示宽高比
    display_aspect_ratio = f"{width}:{height}"

    total_frames = int(fps * duration)
    # 构建滤镜链，使用cosine ease-in-out表达式
    filter_complex = (
        f"[0:v]scale={width}:{height}:force_original_aspect_ratio=increase,"  # scale
        f"crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[img];"
        f"[1:v]scale={width}:{height}[mask];"
        # zoom with cosine ease-in-out
        f"[img]zoompan=z='1+({zmax}-1)*(1-cos(PI*on/{total_frames}))/2':"
        f"x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=1:s={width}x{height},"
        f"setsar=1:1,setdar={width}/{height}[zoomed];"
        f"[zoomed][mask]alphamerge[out]"
    )

    # 构建FFmpeg命令
    cmd = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-i", image_path,
        "-loop", "1", "-i", mask_path,
        "-filter_complex", filter_complex,
        "-map", "[out]",
        "-t", str(duration),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", display_aspect_ratio,
        output_path
    ]

    subprocess.run(cmd, check=True)
    return output_path

def effect_C_ffmpeg(
    image_path: str,
    output_path: str,
    width: int,
    height: int,
    duration: float,
    fps: int,
    temp_dir: str
) -> str:
    """38.2% 黄金径向遮罩 + 轻微垂直滚动 (deceleration)"""
    p = CONFIG['effect_C']
    ratio, df = p['ratio'], p['distance_factor']

    # 创建遮罩图像
    mask_path = os.path.join(temp_dir, 'mask.png')
    create_mask_image((width, height), ratio, "circular", (0.5, 0.5),
                     "horizontal", CONFIG['LOW_RES_FACTOR'], mask_path)

    # 计算显示宽高比
    display_aspect_ratio = f"{width}:{height}"

    # Compute total frames and initial zoom
    total_frames = int(fps * duration)
    z_start = 1 + df  # initial zoom factor to hide edges
    y_move = height * df  # total vertical movement in pixels

    # Build zoom-pan filter: simultaneous zoom-out and upward pan
    filter_complex = (
        f"[0:v]scale={width}:{height}:force_original_aspect_ratio=increase,crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[img];"
        f"[1:v]scale={width}:{height}[mask];"
        f"[img]zoompan="
        f"z='{z_start} - ({z_start}-1)*(on/{total_frames})':"
        f"x='0':"
        f"y='-({y_move})*(on/{total_frames})':"
        f"d={total_frames}:s={width}x{height},"
        f"setsar=1:1,setdar={width}/{height}[mov];"
        f"[mov][mask]alphamerge[out]"
    )

    # 构建FFmpeg命令
    cmd = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-i", image_path,
        "-loop", "1", "-i", mask_path,
        "-filter_complex", filter_complex,
        "-map", "[out]",
        "-t", str(duration),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", display_aspect_ratio,
        output_path
    ]

    subprocess.run(cmd, check=True)
    return output_path

def effect_D_ffmpeg(
    image_path: str,
    output_path: str,
    width: int,
    height: int,
    duration: float,
    fps: int,
    temp_dir: str
) -> str:
    """
    Left‑to‑Right pan‑reveal with constant zoom, preserving vertical content.

    1. The image is scaled preserving its original aspect ratio, ensuring the
       scaled width is `cfg_zoom` times the target video width.
    2. During the clip, the view pans smoothly horizontally across the scaled image.
    3. Vertical cropping is minimized by centering the crop if the scaled image
       height exceeds the target video height.
    """
    # ------------------------------------------------------------------ #
    # parameters
    # ------------------------------------------------------------------ #
    cfg_zoom = CONFIG["effect_D"].get("zoom", 1.5)  # 缩放倍数
    dur = max(duration, CONFIG["effect_D"]["duration"])  # 确保最小时长
    total_frames = int(round(dur * fps))

    # 计算显示宽高比
    display_aspect_ratio = f"{width}:{height}"

    # 获取原始图片信息
    try:
        probestr = subprocess.check_output([
            "ffprobe", "-v", "error",
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height",
            "-of", "csv=p=0:s=x",
            image_path
        ], text=True, stderr=subprocess.PIPE).strip()
        if 'x' not in probestr:
             raise ValueError(f"ffprobe failed to get dimensions for {image_path}. Output: {probestr}")
        img_width, img_height = map(int, probestr.split('x'))
        if img_width <= 0 or img_height <= 0:
             raise ValueError(f"Invalid image dimensions probed: {img_width}x{img_height}")
        logger.debug(f"原始图片尺寸: {img_width}x{img_height}")
        source_aspect_ratio = img_width / img_height
    except subprocess.CalledProcessError as e:
        logger.error(f"ffprobe failed for {image_path}: {e.stderr.decode()}")
        raise
    except Exception as e:
        logger.error(f"Error probing image dimensions for {image_path}: {e}")
        raise

    # 计算目标缩放尺寸，保持原始宽高比
    scaled_width = int(width * cfg_zoom)
    scaled_height = int(scaled_width / source_aspect_ratio)

    # 水平方向的最大可移动距离
    max_x_offset = max(0, scaled_width - width)

    # 垂直方向的居中裁剪偏移（如果需要）
    crop_y_offset = max(0, (scaled_height - height) / 2)

    logger.debug(f"效果D参数: 缩放={cfg_zoom}倍, 源尺寸={img_width}x{img_height} ({source_aspect_ratio:.2f}), "
                 f"目标视频={width}x{height}, 缩放后={scaled_width}x{scaled_height}, "
                 f"最大X平移={max_x_offset}, Y裁剪偏移={crop_y_offset:.1f}")

    # 使用 scale 和 crop 组合，确保垂直居中
    # 使用更流畅的余弦函数实现缓入缓出平移效果
    filter_complex = (
        f"[0:v]scale={scaled_width}:{scaled_height},"           # 保持原始比例放大图像
        f"crop={width}:{height}:"                           # 裁剪到输出尺寸
        f"{max_x_offset}*(0.5-0.5*cos(PI*t/{dur})):"       # 水平从左到右平滑移动 (余弦缓入缓出)
        f"{crop_y_offset},"                                  # 垂直居中裁剪偏移
        f"setsar=1:1,setdar={width}/{height}[out]"           # 设置最终的宽高比
    )

    # 构建FFmpeg命令
    cmd = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-i", image_path,
        "-filter_complex", filter_complex,
        "-map", "[out]",
        "-t", str(dur),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", display_aspect_ratio,
        output_path
    ]

    # 执行命令
    try:
        logger.debug(f"执行效果D (保持垂直内容) 命令")
        subprocess.run(cmd, check=True, capture_output=True)
        logger.debug(f"效果D应用成功")
    except subprocess.CalledProcessError as e:
        logger.error(f"执行效果D命令失败:")
        logger.error(f"命令: {' '.join(cmd)}")
        logger.error(f"错误: {e.stderr.decode() if hasattr(e, 'stderr') and e.stderr else str(e)}")
        raise

    return output_path


def effect_F_ffmpeg(
    image_path: str,
    output_path: str,
    width: int,
    height: int,
    duration: float,
    fps: int,
    temp_dir: str
) -> str:
    """Ken Burns: scroll→zoom→reverse scroll + 对角线遮罩"""
    p = CONFIG['effect_F']
    ratio, zmax, t1, t2 = p['ratio'], p['zoom_max'], p['phase1'], p['phase2']

    # 创建遮罩图像
    mask_path = os.path.join(temp_dir, 'mask.png')
    create_mask_image((width, height), ratio, "linear", (0.5, 0.5),
                     "diagonal", CONFIG['LOW_RES_FACTOR'], mask_path)

    # 计算显示宽高比
    display_aspect_ratio = f"{width}:{height}"

    # 分段：30% 聚焦(放大→平移)、40% 拉远(缩小)、30% 回归(反向平移)
    phase1_duration = max(duration * 0.3, MIN_PHASE_DURATION)
    phase2_duration = max(duration * 0.4, MIN_PHASE_DURATION)
    phase3_duration = max(duration * 0.3, MIN_PHASE_DURATION)

    # Pan offset to center when zoomed
    x_offset = (zmax - 1) * width / 2

    # 如果总时长超过所需时长，按比例缩放各阶段时长
    total_required = phase1_duration + phase2_duration + phase3_duration
    if total_required > duration:
        logger.info(f"调整Ken Burns效果阶段时长：总需要{total_required}秒，可用{duration}秒")
        factor = duration / total_required
        phase1_duration *= factor
        phase2_duration *= factor
        phase3_duration *= factor

    # 构建各阶段的滤镜路径
    phase1_path = os.path.join(temp_dir, 'phase1.mp4')
    phase2_path = os.path.join(temp_dir, 'phase2.mp4')
    phase3_path = os.path.join(temp_dir, 'phase3.mp4')

    # 第一阶段 (smooth zoom in and pan to center with cosine ease-in + mask)
    total_frames1 = int(phase1_duration * fps)
    phase1_filter = (
        f"[0:v]scale={width}:{height}:force_original_aspect_ratio=increase,crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[img1];"
        f"[1:v]scale={width}:{height}[ms1];"
        f"[img1]zoompan="
        f"z='1+({zmax}-1)*(1-cos(PI*on/{total_frames1}))/2':"
        f"x='({x_offset})*(1-cos(PI*on/{total_frames1}))/2':"
        f"y='0':"
        f"d={total_frames1}:s={width}x{height},"
        f"setsar=1:1,setdar={width}/{height}[mov1];"
        f"[mov1][ms1]alphamerge[out1]"
    )

    # 第二阶段 (hold pan, zoom out to 1.0 + mask) -- Smooth horizontal pan
    total_frames2 = int(phase2_duration * fps)
    phase2_filter = (
        f"[0:v]scale={width}:{height}:force_original_aspect_ratio=increase,crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[img2];"
        f"[1:v]scale={width}:{height}[ms2];"
        f"[img2]zoompan="
        f"z='{zmax} - ({zmax}-1)*(1-cos(PI*on/{total_frames2}))/2':"
        f"x='{x_offset} - ({x_offset})*(1-cos(PI*on/{total_frames2}))/2':"
        f"y='0':"
        f"d={total_frames2}:s={width}x{height},"
        f"setsar=1:1,setdar={width}/{height}[mov2];"
        f"[mov2][ms2]alphamerge[out2]"
    )

    # 第三阶段 (pan back to start + mask)
    total_frames3 = int(phase3_duration * fps)
    phase3_filter = (
        f"[0:v]scale={width}:{height}:force_original_aspect_ratio=increase,crop={width}:{height}:(in_w-{width})/2:(in_h-{height})/2[img3];"
        f"[1:v]scale={width}:{height}[ms3];"
        f"[img3]zoompan="
        f"z='1':"
        f"x='({x_offset})*((1+cos(PI*on/{total_frames3}))/2)':"
        f"y='0':"
        f"d={total_frames3}:s={width}x{height},"
        f"setsar=1:1,setdar={width}/{height}[mov3];"
        f"[mov3][ms3]alphamerge[out3]"
    )

    # 生成三个阶段的视频
    # 第一阶段
    cmd1 = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-framerate", str(fps), "-i", image_path,
        "-loop", "1", "-framerate", str(fps), "-i", mask_path,
        "-filter_complex", phase1_filter,
        "-map", "[out1]",
        "-t", str(phase1_duration),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", display_aspect_ratio,
        phase1_path
    ]

    # 第二阶段
    cmd2 = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-framerate", str(fps), "-i", image_path,
        "-loop", "1", "-framerate", str(fps), "-i", mask_path,
        "-filter_complex", phase2_filter,
        "-map", "[out2]",
        "-t", str(phase2_duration),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", display_aspect_ratio,
        phase2_path
    ]

    # 第三阶段
    cmd3 = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-framerate", str(fps), "-i", image_path,
        "-loop", "1", "-framerate", str(fps), "-i", mask_path,
        "-filter_complex", phase3_filter,
        "-map", "[out3]",
        "-t", str(phase3_duration),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", display_aspect_ratio,
        phase3_path
    ]

    # 连接三个阶段
    concat_file = os.path.join(temp_dir, 'concat.txt')
    with open(concat_file, 'w') as f:
        f.write(f"file '{phase1_path}'\n")
        f.write(f"file '{phase2_path}'\n")
        f.write(f"file '{phase3_path}'\n")

    concat_cmd = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-f", "concat",
        "-safe", "0",
        "-i", concat_file,
        "-c:v", "libx264", "-r", str(fps), "-pix_fmt", "yuv420p", "-movflags", "+faststart",
        output_path
    ]

    # 执行命令
    subprocess.run(cmd1, check=True)
    subprocess.run(cmd2, check=True)
    subprocess.run(cmd3, check=True)
    subprocess.run(concat_cmd, check=True)

    return output_path

# --- Vertical Ken Burns effect for portrait images ---
def effect_vertical_ffmpeg(
    image_path: str,
    output_path: str,
    width: int,
    height: int,
    duration: float,
    fps: int,
    temp_dir: str,
    direction: str = "down"  # 方向参数: "down"(从上往下), "up"(从下往上)
) -> str:
    """
    为竖向或方形图片应用纯粹的垂直平移特效。
    保持原始宽高比，按宽度等比例缩放，然后应用垂直移动。

    Args:
        direction: 垂直移动方向，可选值: "down"(从上往下), "up"(从下往上)
    """
    # 验证方向参数
    if direction not in ["down", "up"]:
        logger.warning(f"无效的垂直方向参数 '{direction}'，默认使用 'down'")
        direction = "down"

    # 获取基本参数
    min_dur = EFFECTS_CATALOG["vertical"]["min_duration"]
    dur = max(duration, min_dur)

    # 获取原始图片尺寸
    try:
        probestr = subprocess.check_output(
            ["ffprobe", "-v", "error", "-select_streams", "v:0",
             "-show_entries", "stream=width,height",
             "-of", "csv=p=0:s=x", image_path],
            text=True,
            stderr=subprocess.PIPE
        ).strip()
        if 'x' not in probestr:
            raise ValueError(f"无法获取图片尺寸信息: {probestr}")
        iw, ih = map(int, probestr.split('x'))
        logger.debug(f"原始图片尺寸: {iw}x{ih}")
        if iw <= 0 or ih <= 0:
            raise ValueError(f"图片尺寸无效: {iw}x{ih}")
    except Exception as e:
        logger.error(f"读取图片尺寸时出错: {str(e)}")
        raise

    # 计算缩放后的预计高度（在宽度缩放到width后）
    estimated_in_h = ih * width / iw

    # 确保有足够的垂直移动空间
    # 如果缩放后高度不足，应用额外放大因子
    zoom_factor = 1.0
    if estimated_in_h < height * 1.3:  # 至少需要比目标高度高30%
        zoom_factor = (height * 1.3) / estimated_in_h
        logger.info(f"图片高度不足，应用额外放大系数: {zoom_factor:.2f}倍")

    # 计算最终缩放尺寸
    scaled_width = width
    scaled_height = int(estimated_in_h * zoom_factor)

    # 计算最大可移动距离
    move_range = max(0, scaled_height - height)

    # 计算总帧数
    total_fr = int(round(dur * fps))

    # 根据direction参数构建过滤器表达式
    # 使用余弦缓动以实现平滑效果
    if direction == "up":
        # 从下到上移动：从底部开始，向上移动
        filter_complex = (
            f"[0:v]scale={scaled_width}:{scaled_height},"  # 按比例缩放并应用缩放因子
            f"crop={width}:{height}:(iw-{width})/2:"  # 横向居中裁剪
            f"{move_range}*(0.5+0.5*cos(PI*n/{total_fr-1})),"  # 修正：使用余弦缓动从1到0
            f"setsar=1:1,setdar={width}/{height}"  # 设置显示比例
        )
        logger.debug(f"应用上移特效(UP): 缩放={scaled_width}x{scaled_height}, 移动范围={move_range}px")
    else:  # "down"或默认
        # 从上到下移动：从顶部开始，向下移动
        filter_complex = (
            f"[0:v]scale={scaled_width}:{scaled_height},"  # 按比例缩放并应用缩放因子
            f"crop={width}:{height}:(iw-{width})/2:"  # 横向居中裁剪
            f"{move_range}*(0.5-0.5*cos(PI*n/{total_fr-1})),"  # 使用余弦缓动
            f"setsar=1:1,setdar={width}/{height}"  # 设置显示比例
        )
        logger.debug(f"应用下移特效(DOWN): 缩放={scaled_width}x{scaled_height}, 移动范围={move_range}px")

    # 构建FFmpeg命令
    cmd = [
        "ffmpeg", "-y",
        "-hide_banner", "-loglevel", "error",
        "-loop", "1", "-i", image_path,
        "-filter_complex", filter_complex,
        "-t", str(dur),
        "-r", str(fps),
        "-pix_fmt", "yuv420p",
        "-aspect", f"{width}:{height}",
        output_path
    ]

    # 执行FFmpeg命令
    try:
        logger.debug(f"执行FFmpeg命令：纯垂直{direction}移动")
        #subprocess.run(cmd, check=True, capture_output=True)
        subprocess.run(cmd, check=True)
        logger.debug(f"垂直{direction}移动效果应用成功")
    except subprocess.CalledProcessError as e:
        logger.error(f"执行FFmpeg命令失败:")
        logger.error(f"命令: {' '.join(cmd)}")
        logger.error(f"错误: {e.stderr.decode() if e.stderr else 'Unknown error'}")
        raise
    return output_path

def create_mask_image(size, ratio, shape, origin, direction, low_res_factor, output_path):
    """创建遮罩图像并保存到文件"""
    mask = get_mask(size, ratio, shape, origin, direction, low_res_factor)

    # 将单通道 float32 转换为 RGBA 图像
    h, w = mask.shape
    rgba = np.zeros((h, w, 4), dtype=np.uint8)

    # 设置 alpha 通道
    rgba[..., 3] = (mask * 255).astype(np.uint8)
    # RGB 通道全白
    rgba[..., 0:3] = 255

    cv2.imwrite(output_path, rgba)
    return output_path

# --- 新增：核心特效选择逻辑 ---
def select_effect(
    duration: float,
    image_width: int,
    image_height: int,
    prev_effect: Optional[str] = None
) -> Tuple[str, Dict[str, Any]]:
    """
    根据时长、图片尺寸和上一个特效，选择一个合适的特效。

    Args:
        duration: 当前片段的时长 (秒)
        image_width: 原始图片宽度
        image_height: 原始图片高度
        prev_effect: 上一个片段使用的特效键 (来自 EFFECTS_CATALOG)

    Returns:
        Tuple[str, Dict[str, Any]]: (选择的特效键, 特效的配置字典)
    """
    global _LAST_VERTICAL_DIRECTION
    # 计算图片的宽高比
    aspect_ratio = image_width / max(1, image_height)  # 避免除零
    logger.debug(f"图片宽高比: {aspect_ratio:.2f}:1")

    # 定义宽高比阈值
    WIDE_ASPECT_RATIO_THRESHOLD = 1.8  # 16:9 = 1.78:1
    ULTRA_WIDE_THRESHOLD = 2.0  # 2:1 或更宽的全景图片
    SQUARE_THRESHOLD_LOW = 0.9
    SQUARE_THRESHOLD_HIGH = 1.1 # Aspect ratios between 0.9 and 1.1 are considered square-like

    # 判断图片类型
    is_vertical = image_height > image_width
    is_square_like = SQUARE_THRESHOLD_LOW <= aspect_ratio <= SQUARE_THRESHOLD_HIGH
    is_wide = aspect_ratio > SQUARE_THRESHOLD_HIGH # 排除方形
    is_ultra_wide = aspect_ratio >= ULTRA_WIDE_THRESHOLD

    # 1. 处理纵向图片和方形图片，使用vertical特效
    if is_vertical or is_square_like:
        effect_name = "vertical"
        logger.debug(f"检测到{'纵向' if is_vertical else '方形'}图片，使用vertical特效")

        # 确保vertical特效满足时长要求
        if duration >= EFFECTS_CATALOG[effect_name]["min_duration"]:
            # 准备方向参数，方形图片要轮换方向
            effect_params = {}

            # 如果是方形图片，轮换上下方向
            if is_square_like:
                # 轮换方向机制
                # 选择与上次相反的方向
                if prev_effect == "vertical":
                    # 如果上一个也是vertical，选择相反方向
                    next_direction = "up" if _LAST_VERTICAL_DIRECTION == "down" else "down"
                    logger.debug(f"连续方形图片，轮换vertical方向：从{_LAST_VERTICAL_DIRECTION}到{next_direction}")
                    _LAST_VERTICAL_DIRECTION = next_direction
                else:
                    # 如果上一个不是vertical，使用默认方向
                    _LAST_VERTICAL_DIRECTION = "down"
                # 设置方向参数
                effect_params["direction"] = _LAST_VERTICAL_DIRECTION
                logger.debug(f"为方形图片设置vertical方向: {_LAST_VERTICAL_DIRECTION}")
            else:
                # 纵向图片默认上下移动
                effect_params["direction"] = "down"
                logger.debug(f"为纵向图片设置vertical方向: down")

            config = EFFECTS_CATALOG[effect_name].copy()
            config["effect_params"] = effect_params
            return effect_name, config
        else:
            logger.warning(f"图片时长 {duration}s 不满足 vertical 特效最低要求 {EFFECTS_CATALOG['vertical']['min_duration']}s，将尝试其他特效")
            # 继续常规特效选择逻辑

    # 2. 筛选出满足最低时长要求的非纵向特效
    available_keys = [
        key for key, config in EFFECTS_CATALOG.items()
        if duration >= config["min_duration"] and key != "vertical" and config["is_advanced"]  # 只考虑高级特效
    ]
    logger.debug(f"满足时长 {duration}s 的可用高级特效: {available_keys}")

    # 3. 根据图片宽高比进行特效筛选
    effects_to_exclude = []
    if is_square_like:
        logger.debug(f"检测到方形图片 (宽高比 {aspect_ratio:.2f})，排除裁剪严重的效果 A, C")
        effects_to_exclude.extend(["A", "C"])
    elif not is_wide: # 如果不是纵向也不是宽幅（可能是略窄或略方），也排除 D
         if aspect_ratio < WIDE_ASPECT_RATIO_THRESHOLD:
             logger.debug(f"图片不够宽 (宽高比 {aspect_ratio:.2f})，排除效果 D")
             effects_to_exclude.append("D")
    else: # 宽幅图片
        if aspect_ratio < WIDE_ASPECT_RATIO_THRESHOLD:
            logger.debug(f"图片不够宽 (宽高比 {aspect_ratio:.2f})，排除效果 D")
            effects_to_exclude.append("D")

    if effects_to_exclude:
        original_available_count = len(available_keys)
        available_keys = [k for k in available_keys if k not in effects_to_exclude]
        if len(available_keys) < original_available_count:
            logger.debug(f"排除 {effects_to_exclude} 后，剩余可用特效: {available_keys}")
        else:
            logger.debug(f"特效 {effects_to_exclude} 不在可用列表或已被排除")

    # 4. 如果图片特别宽 (超过2:1) 且时长超过阈值，直接选择效果D
    if is_ultra_wide and duration >= D_PREFER_DURATION_THRESHOLD and "D" in available_keys:
        logger.debug(f"检测到超宽幅图片 (宽高比 {aspect_ratio:.2f})，且时长超过阈值 {D_PREFER_DURATION_THRESHOLD}s，直接选择效果D")
        return "D", EFFECTS_CATALOG["D"]

    # 5. 排除上一个使用的特效
    eligible_keys = [key for key in available_keys if key != prev_effect]
    if len(eligible_keys) < len(available_keys):
        logger.debug(f"排除上一个特效 '{prev_effect}' 后，剩余可选特效: {eligible_keys}")
    else:
        logger.debug(f"上一个特效 '{prev_effect}' 不在可用列表或已被排除，可选特效不变: {eligible_keys}")

    # 6. 如果排除后没有可选特效，则恢复所有可用特效 (忽略上一个特效的限制)
    if not eligible_keys and available_keys:
        logger.debug(f"无法排除上一个特效 '{prev_effect}' (没有其他选项)，从所有满足条件的特效中选择")
        eligible_keys = available_keys

    # 7. 如果仍然没有可选特效（时长过短或宽高比限制），执行回退逻辑
    if not eligible_keys:
        logger.warning(f"没有找到适合时长 {duration}s 和宽高比 {aspect_ratio:.2f} 的特效。尝试回退...")

        # 如果是方形图片，尝试降低vertical特效的时长要求
        if is_square_like:
            # 降低时长要求，允许更短时长使用vertical
            min_vertical_duration = EFFECTS_CATALOG["vertical"]["min_duration"]
            if duration >= min_vertical_duration * 0.8:  # 允许降低20%的时长要求
                logger.debug(f"方形图片：降低vertical特效时长要求至{min_vertical_duration * 0.8:.2f}s")
                # 为方形图片选择方向
                if prev_effect == "vertical":
                    # 如果上一个也是vertical，选择相反方向
                    next_direction = "up" if _LAST_VERTICAL_DIRECTION == "down" else "down"
                    _LAST_VERTICAL_DIRECTION = next_direction
                else:
                    _LAST_VERTICAL_DIRECTION = "down"  # 默认方向
                effect_params = {"direction": _LAST_VERTICAL_DIRECTION}
                logger.debug(f"为方形图片设置vertical降低时长要求特效，方向: {_LAST_VERTICAL_DIRECTION}")
                return "vertical", {"effect_params": effect_params}

        # 高级特效回退逻辑
        fallback_keys = []
        if is_square_like:
            # 方形图片回退优先级: B > F > A (如果A存在且符合时长)
            fallback_priority = ["B", "F", "A"]
            fallback_keys = [k for k in fallback_priority if k in EFFECTS_CATALOG and duration >= EFFECTS_CATALOG[k]["min_duration"]]
            logger.debug(f"方形图片回退选项 (按优先级): {fallback_keys}")
        else:
            # 其他情况回退优先级: A > B > C > F (如果符合时长)
            fallback_priority = ["A", "B", "C", "F"]
            fallback_keys = [k for k in fallback_priority if k in EFFECTS_CATALOG and duration >= EFFECTS_CATALOG[k]["min_duration"]]
            logger.debug(f"非方形图片回退选项 (按优先级): {fallback_keys}")

        if fallback_keys:
            eligible_keys = fallback_keys # 使用回退列表
            logger.warning(f"使用回退特效列表: {eligible_keys}")
        else:
            # 极端情况：没有任何特效满足时长，强制使用 'A'
            logger.error(f"没有找到任何满足最低时长的特效！强制回退到 'A'，时长可能不足！")
            eligible_keys = ["A"]

    # 8. 最终选择特效
    selected_key = None
    # 从剩余的合格特效中选择 (如果是回退列表，则按优先级取第一个)
    if len(eligible_keys) > 0:
       # 如果是通过回退逻辑产生的列表，按优先级选择第一个
       # 否则（正常筛选流程），随机选择
       if 'fallback_priority' in locals(): # 判断是否进入了回退逻辑
           selected_key = eligible_keys[0]
           logger.debug(f"从回退列表中按优先级选择: {selected_key}")
       else:
           selected_key = random.choice(eligible_keys)
           logger.debug(f"从合格列表中随机选择: {selected_key}")
    else:
        # 理论上不应到达这里，因为回退逻辑保证至少有'A'
        logger.error("选择特效时发生意外错误，合格列表为空！回退到 'A'")
        selected_key = "A"

    logger.info(f"最终选择特效: {selected_key} (时长: {duration}s, 宽高比: {aspect_ratio:.2f}, 上一个: {prev_effect})")

    # 确保返回的 key 在 EFFECTS_CATALOG 中
    if selected_key not in EFFECTS_CATALOG:
        logger.error(f"选择的特效 '{selected_key}' 不在 EFFECTS_CATALOG 中！强制返回 'A'")
        selected_key = "A"

    return selected_key, EFFECTS_CATALOG[selected_key]