#!/usr/bin/env python3
"""
测试图片抖动修复效果的脚本
"""

import os
import sys
import tempfile
import logging
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, '/Users/<USER>/ai-video')

from image2clip import generate_video_from_image

# 设置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_image_jitter_fix():
    """测试高分辨率图片的抖动修复"""
    
    # 测试图片路径
    high_res_image = "/Users/<USER>/ai-video/1-theme-talker/results/Putten_murder_case_Netherlands/images/Buorren_Putten_by_de_tsjerke_1.JPG"
    low_res_image = "/Users/<USER>/ai-video/1-theme-talker/results/Putten_murder_case_Netherlands/ai_images/Putten_murder_case_Netherlands_images_23_2.png"
    
    # 检查文件是否存在
    if not os.path.exists(high_res_image):
        logger.error(f"高分辨率测试图片不存在: {high_res_image}")
        return False
        
    if not os.path.exists(low_res_image):
        logger.error(f"低分辨率测试图片不存在: {low_res_image}")
        return False
    
    # 创建输出目录
    output_dir = Path("/Users/<USER>/ai-video/test_outputs")
    output_dir.mkdir(exist_ok=True)
    
    # 测试参数
    duration = 5.0  # 5秒测试视频
    
    try:
        # 测试高分辨率图片
        logger.info("=== 测试高分辨率图片 (应该应用抖动修复) ===")
        high_res_output = output_dir / "test_high_res_fixed.mp4"
        result1 = generate_video_from_image(
            image_path=high_res_image,
            output_path=str(high_res_output),
            duration=duration,
            use_crop=False
        )
        logger.info(f"高分辨率图片测试完成: {result1}")
        
        # 测试低分辨率图片
        logger.info("=== 测试低分辨率图片 (正常处理) ===")
        low_res_output = output_dir / "test_low_res_normal.mp4"
        result2 = generate_video_from_image(
            image_path=low_res_image,
            output_path=str(low_res_output),
            duration=duration,
            use_crop=False
        )
        logger.info(f"低分辨率图片测试完成: {result2}")
        
        # 检查输出文件
        if os.path.exists(high_res_output) and os.path.exists(low_res_output):
            high_res_size = os.path.getsize(high_res_output) / (1024 * 1024)
            low_res_size = os.path.getsize(low_res_output) / (1024 * 1024)
            
            logger.info(f"测试结果:")
            logger.info(f"  高分辨率视频: {high_res_output} ({high_res_size:.2f}MB)")
            logger.info(f"  低分辨率视频: {low_res_output} ({low_res_size:.2f}MB)")
            logger.info(f"请手动检查视频是否还有抖动问题")
            
            return True
        else:
            logger.error("输出文件生成失败")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        import traceback
        logger.error("错误堆栈:\n" + traceback.format_exc())
        return False

def analyze_image_properties():
    """分析图片属性，帮助理解抖动原因"""
    import subprocess
    
    images = [
        "/Users/<USER>/ai-video/1-theme-talker/results/Putten_murder_case_Netherlands/images/Buorren_Putten_by_de_tsjerke_1.JPG",
        "/Users/<USER>/ai-video/1-theme-talker/results/Putten_murder_case_Netherlands/ai_images/Putten_murder_case_Netherlands_images_23_2.png"
    ]
    
    for img_path in images:
        if os.path.exists(img_path):
            logger.info(f"\n=== 分析图片: {os.path.basename(img_path)} ===")
            
            # 获取基本信息
            try:
                result = subprocess.run([
                    "ffprobe", "-v", "error", 
                    "-select_streams", "v:0",
                    "-show_entries", "stream=width,height,pix_fmt,codec_name",
                    "-of", "csv=p=0", img_path
                ], capture_output=True, text=True, check=True)
                
                codec, width, height, pix_fmt = result.stdout.strip().split(',')
                resolution = int(width) * int(height)
                aspect_ratio = int(width) / int(height)
                
                logger.info(f"  编码格式: {codec}")
                logger.info(f"  分辨率: {width}x{height} ({resolution/1000000:.1f}MP)")
                logger.info(f"  宽高比: {aspect_ratio:.2f}:1")
                logger.info(f"  像素格式: {pix_fmt}")
                logger.info(f"  是否高分辨率: {'是' if resolution > 2000000 else '否'}")
                
            except subprocess.CalledProcessError as e:
                logger.error(f"无法分析图片 {img_path}: {e}")

if __name__ == "__main__":
    logger.info("开始图片抖动修复测试")
    
    # 分析图片属性
    analyze_image_properties()
    
    # 运行测试
    success = test_image_jitter_fix()
    
    if success:
        logger.info("测试完成！请检查生成的视频文件")
    else:
        logger.error("测试失败")
        sys.exit(1)
