#!/usr/bin/env python3
"""
专门测试高分辨率图片抖动修复的脚本
用于手工确认修复效果
"""

import os
import sys
import logging
import subprocess
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, '/Users/<USER>/ai-video')

from image2clip import generate_video_from_image

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_image_details(image_path):
    """分析图片详细信息"""
    logger.info(f"\n=== 分析图片: {os.path.basename(image_path)} ===")
    
    try:
        # 获取基本信息
        result = subprocess.run([
            "ffprobe", "-v", "error", 
            "-select_streams", "v:0",
            "-show_entries", "stream=width,height,pix_fmt,codec_name",
            "-of", "csv=p=0", image_path
        ], capture_output=True, text=True, check=True)
        
        codec, width, height, pix_fmt = result.stdout.strip().split(',')
        resolution = int(width) * int(height)
        aspect_ratio = int(width) / int(height)
        
        logger.info(f"  编码格式: {codec}")
        logger.info(f"  分辨率: {width}x{height} ({resolution/1000000:.1f}MP)")
        logger.info(f"  宽高比: {aspect_ratio:.2f}:1")
        logger.info(f"  像素格式: {pix_fmt}")
        logger.info(f"  是否高分辨率: {'是' if resolution > 2000000 else '否'} (阈值: 2MP)")
        
        # 获取文件大小
        file_size = os.path.getsize(image_path) / (1024 * 1024)
        logger.info(f"  文件大小: {file_size:.2f}MB")
        
        return {
            'width': int(width),
            'height': int(height),
            'resolution': resolution,
            'aspect_ratio': aspect_ratio,
            'codec': codec,
            'pix_fmt': pix_fmt,
            'file_size': file_size,
            'is_high_res': resolution > 2000000
        }
        
    except subprocess.CalledProcessError as e:
        logger.error(f"无法分析图片 {image_path}: {e}")
        return None

def test_high_res_fix():
    """测试高分辨率图片修复效果"""
    
    # 测试图片路径
    high_res_image = "/Users/<USER>/ai-video/1-theme-talker/results/Putten_murder_case_Netherlands/images/Buorren_Putten_by_de_tsjerke_1.JPG"
    
    # 检查文件是否存在
    if not os.path.exists(high_res_image):
        logger.error(f"测试图片不存在: {high_res_image}")
        return False
    
    # 分析图片信息
    img_info = analyze_image_details(high_res_image)
    if not img_info:
        return False
    
    # 创建输出目录
    output_dir = Path("/Users/<USER>/ai-video/test_outputs")
    output_dir.mkdir(exist_ok=True)
    
    # 测试参数
    test_cases = [
        {
            'name': '短时长测试 (2秒)',
            'duration': 2.0,
            'suffix': '2s'
        },
        {
            'name': '中等时长测试 (5秒)',
            'duration': 5.0,
            'suffix': '5s'
        },
        {
            'name': '长时长测试 (8秒)',
            'duration': 8.0,
            'suffix': '8s'
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        logger.info(f"\n=== {test_case['name']} ===")
        
        output_path = output_dir / f"high_res_fixed_{test_case['suffix']}.mp4"
        
        try:
            # 记录开始时间
            import time
            start_time = time.time()
            
            # 生成视频
            logger.info(f"开始生成视频: {output_path}")
            result = generate_video_from_image(
                image_path=high_res_image,
                output_path=str(output_path),
                duration=test_case['duration'],
                use_crop=False  # 不使用裁剪，测试完整的panzoom效果
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if result and os.path.exists(output_path):
                # 分析输出视频
                video_size = os.path.getsize(output_path) / (1024 * 1024)
                
                # 获取视频信息
                video_result = subprocess.run([
                    "ffprobe", "-v", "error",
                    "-select_streams", "v:0", 
                    "-show_entries", "stream=width,height,r_frame_rate,codec_name",
                    "-of", "csv=p=0", str(output_path)
                ], capture_output=True, text=True, check=True)
                
                video_codec, video_width, video_height, frame_rate = video_result.stdout.strip().split(',')
                
                test_result = {
                    'name': test_case['name'],
                    'duration': test_case['duration'],
                    'output_path': output_path,
                    'success': True,
                    'processing_time': processing_time,
                    'video_size': video_size,
                    'video_width': int(video_width),
                    'video_height': int(video_height),
                    'video_codec': video_codec,
                    'frame_rate': frame_rate
                }
                
                logger.info(f"✅ 生成成功!")
                logger.info(f"   处理时间: {processing_time:.2f}秒")
                logger.info(f"   文件大小: {video_size:.2f}MB")
                logger.info(f"   视频规格: {video_width}x{video_height}, {frame_rate}fps, {video_codec}")
                
            else:
                test_result = {
                    'name': test_case['name'],
                    'success': False,
                    'error': 'Video generation failed'
                }
                logger.error(f"❌ 生成失败!")
                
        except Exception as e:
            test_result = {
                'name': test_case['name'],
                'success': False,
                'error': str(e)
            }
            logger.error(f"❌ 生成过程中出错: {str(e)}")
        
        results.append(test_result)
    
    # 生成测试报告
    generate_test_report(img_info, results, output_dir)
    
    return all(r.get('success', False) for r in results)

def generate_test_report(img_info, results, output_dir):
    """生成测试报告"""
    
    report_path = output_dir / "high_res_fix_test_report.md"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# 高分辨率图片抖动修复测试报告\n\n")
        
        f.write("## 测试图片信息\n")
        f.write(f"- **文件**: Buorren_Putten_by_de_tsjerke_1.JPG\n")
        f.write(f"- **分辨率**: {img_info['width']}x{img_info['height']} ({img_info['resolution']/1000000:.1f}MP)\n")
        f.write(f"- **宽高比**: {img_info['aspect_ratio']:.2f}:1\n")
        f.write(f"- **编码格式**: {img_info['codec']}\n")
        f.write(f"- **像素格式**: {img_info['pix_fmt']}\n")
        f.write(f"- **文件大小**: {img_info['file_size']:.2f}MB\n")
        f.write(f"- **高分辨率检测**: {'✅ 是' if img_info['is_high_res'] else '❌ 否'}\n\n")
        
        f.write("## 应用的修复措施\n")
        if img_info['is_high_res']:
            f.write("- ✅ **放大倍数**: 从4倍降到2倍\n")
            f.write("- ✅ **缩放范围**: 减半 (1.0→1.2 变为 1.0→1.1)\n")
            f.write("- ✅ **平移速度**: 减半\n")
            f.write("- ✅ **编码质量**: CRF从23降到21，预设slow\n")
            f.write("- ✅ **像素格式**: 强制yuv420p\n")
            f.write("- ✅ **帧率控制**: 恒定帧率(cfr)\n\n")
        else:
            f.write("- ❌ 未检测为高分辨率图片，使用标准处理\n\n")
        
        f.write("## 测试结果\n\n")
        
        for result in results:
            f.write(f"### {result['name']}\n")
            if result['success']:
                f.write(f"- **状态**: ✅ 成功\n")
                f.write(f"- **时长**: {result['duration']}秒\n")
                f.write(f"- **处理时间**: {result['processing_time']:.2f}秒\n")
                f.write(f"- **文件大小**: {result['video_size']:.2f}MB\n")
                f.write(f"- **视频规格**: {result['video_width']}x{result['video_height']}\n")
                f.write(f"- **编码**: {result['video_codec']}\n")
                f.write(f"- **帧率**: {result['frame_rate']}\n")
                f.write(f"- **输出文件**: `{result['output_path'].name}`\n\n")
            else:
                f.write(f"- **状态**: ❌ 失败\n")
                f.write(f"- **错误**: {result.get('error', '未知错误')}\n\n")
        
        f.write("## 手工验证建议\n\n")
        f.write("请手工播放生成的视频文件，检查以下方面：\n\n")
        f.write("1. **抖动情况**: 观察是否还有明显的抖动或跳跃\n")
        f.write("2. **运动平滑度**: 缩放和平移是否平滑自然\n")
        f.write("3. **图像质量**: 是否有明显的质量下降或伪影\n")
        f.write("4. **边缘处理**: 图片边缘是否有黑边或异常\n")
        f.write("5. **整体效果**: 与修复前的效果对比\n\n")
        
        f.write("## 对比测试\n\n")
        f.write("建议与以下图片生成的视频进行对比：\n")
        f.write("- **低分辨率图片**: `Putten_murder_case_Netherlands_images_23_2.png` (960x536)\n")
        f.write("- **其他高分辨率图片**: 如果有的话\n\n")
        
        f.write("---\n")
        f.write(f"*报告生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")
    
    logger.info(f"\n📋 测试报告已生成: {report_path}")

if __name__ == "__main__":
    logger.info("🚀 开始高分辨率图片抖动修复测试")
    
    success = test_high_res_fix()
    
    if success:
        logger.info("\n✅ 所有测试完成！请查看生成的视频文件和测试报告")
        logger.info("📁 输出目录: /Users/<USER>/ai-video/test_outputs/")
        logger.info("📋 测试报告: high_res_fix_test_report.md")
        logger.info("\n🎬 请手工播放视频文件验证抖动修复效果")
    else:
        logger.error("\n❌ 部分测试失败，请查看错误信息")
        sys.exit(1)
